// AI推荐组件 - 显示AI推荐的标签和文件夹

import React, { useState, useEffect } from 'react'
import {
  Sparkles,
  Tag,
  Folder,
  Plus,
  Check,
  Loader2,
  RefreshCw,
  AlertCircle,
  FileText
} from 'lucide-react'

// shadcn组件导入
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'

/**
 * AI推荐请求接口
 */
interface AIRecommendationRequest {
  title?: string
  url?: string
  content?: string
  description?: string
  maxRecommendations?: number
}

/**
 * 标签推荐响应接口
 */
interface TagRecommendationResponse {
  existingTags: string[]
  newTags: string[]
  confidence: number
  reasoning?: string
}

/**
 * 文件夹推荐响应接口
 */
interface FolderRecommendationResponse {
  recommendedFolders: Array<{
    name: string
    confidence: number
    reason?: string
  }>
  reasoning?: string
}

/**
 * 描述生成响应接口
 */
interface DescriptionGenerationResponse {
  description: string
  confidence: number
  wordCount: number
  reasoning?: string
}

/**
 * AI推荐组件属性接口
 */
interface AIRecommendationsProps {
  /** 推荐请求数据 */
  request: AIRecommendationRequest
  /** 当前已选择的标签 */
  selectedTags?: string[]
  /** 当前选择的文件夹 */
  selectedFolder?: string
  /** 当前描述内容 */
  currentDescription?: string
  /** 标签选择回调 */
  onTagSelect?: (tag: string) => void
  /** 标签取消选择回调 */
  onTagDeselect?: (tag: string) => void
  /** 文件夹选择回调 */
  onFolderSelect?: (folder: string) => void
  /** 描述选择回调 */
  onDescriptionSelect?: (description: string) => void
  /** 一键接受所有推荐回调 */
  onAcceptAll?: (data: { tags: string[], folder?: string, description?: string }) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示标签推荐 */
  showTagRecommendations?: boolean
  /** 是否显示文件夹推荐 */
  showFolderRecommendations?: boolean
  /** 是否显示描述生成 */
  showDescriptionGeneration?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * AI推荐组件
 * 提供基于AI的标签和文件夹推荐功能
 */
const AIRecommendations: React.FC<AIRecommendationsProps> = ({
  request,
  selectedTags = [],
  selectedFolder,
  currentDescription,
  onTagSelect,
  onTagDeselect,
  onFolderSelect,
  onDescriptionSelect,
  onAcceptAll,
  disabled = false,
  showTagRecommendations = true,
  showFolderRecommendations = true,
  showDescriptionGeneration = true,
  className = ''
}) => {
  const [loading, setLoading] = useState(false)
  const [tagRecommendations, setTagRecommendations] = useState<TagRecommendationResponse | null>(null)
  const [folderRecommendations, setFolderRecommendations] = useState<FolderRecommendationResponse | null>(null)
  const [descriptionGeneration, setDescriptionGeneration] = useState<DescriptionGenerationResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [descriptionLoading, setDescriptionLoading] = useState(false)
  const [hasAcceptedAll, setHasAcceptedAll] = useState(false) // 跟踪是否已执行一键接受

  /**
   * 获取AI推荐
   */
  const fetchRecommendations = async () => {
    if (!request.title && !request.content && !request.description) {
      return
    }

    // 如果已经执行了一键接受，则不再自动获取推荐
    if (hasAcceptedAll) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('获取AI推荐:', request)

      // 发送消息到background script - 使用新的全面推荐
      const response = await chrome.runtime.sendMessage({
        type: 'AI_RECOMMEND_ALL',
        data: request
      })

      if (response?.success) {
        setTagRecommendations(response.data.tags)
        setFolderRecommendations(response.data.folders)
        setDescriptionGeneration(response.data.description)
        console.log('AI推荐获取成功:', response.data)
      } else {
        throw new Error(response?.error || 'AI推荐失败')
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error)
      setError(error instanceof Error ? error.message : '获取推荐失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 刷新推荐
   */
  const handleRefresh = () => {
    fetchRecommendations()
  }

  /**
   * 生成描述
   */
  const generateDescription = async () => {
    if (!request.title && !request.content && !request.url) {
      return
    }

    try {
      setDescriptionLoading(true)
      setError(null)

      console.log('生成AI描述:', request)

      // 发送消息到background script
      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_DESCRIPTION',
        data: {
          title: request.title,
          url: request.url,
          content: request.content || request.description,
          maxLength: 200
        }
      })

      if (response?.success) {
        setDescriptionGeneration(response.data)
        console.log('AI描述生成成功:', response.data)
      } else {
        throw new Error(response?.error || 'AI描述生成失败')
      }
    } catch (error) {
      console.error('生成AI描述失败:', error)
      setError(error instanceof Error ? error.message : '生成描述失败')
    } finally {
      setDescriptionLoading(false)
    }
  }

  /**
   * 应用生成的描述
   */
  const handleApplyDescription = () => {
    if (descriptionGeneration && onDescriptionSelect) {
      onDescriptionSelect(descriptionGeneration.description)
      setDescriptionGeneration(null) // 应用后清除
    }
  }

  /**
   * 处理标签点击
   */
  const handleTagClick = (tag: string) => {
    if (disabled) return

    if (selectedTags.includes(tag)) {
      onTagDeselect?.(tag)
    } else {
      onTagSelect?.(tag)
    }
  }

  /**
   * 处理文件夹点击
   */
  const handleFolderClick = (folder: string) => {
    if (disabled) return
    onFolderSelect?.(folder)
  }

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-gray-600'
  }

  /**
   * 获取置信度文本
   */
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  /**
   * 一键接受所有推荐
   */
  const handleAcceptAll = () => {
    if (!onAcceptAll) return

    const acceptData: { tags: string[], folder?: string, description?: string } = {
      tags: []
    }

    // 收集所有推荐的标签
    if (tagRecommendations) {
      acceptData.tags = [
        ...tagRecommendations.existingTags,
        ...tagRecommendations.newTags
      ]
    }

    // 收集推荐的文件夹（取第一个推荐）
    if (folderRecommendations && folderRecommendations.recommendedFolders.length > 0) {
      acceptData.folder = folderRecommendations.recommendedFolders[0].name
    }

    // 收集生成的描述
    if (descriptionGeneration) {
      acceptData.description = descriptionGeneration.description
    }

    // 调用回调函数
    onAcceptAll(acceptData)

    // 设置已接受状态，阻止后续的自动推荐
    setHasAcceptedAll(true)
  }

  /**
   * 检查是否有推荐内容可以接受
   */
  const hasRecommendations = () => {
    const hasTags = tagRecommendations &&
      (tagRecommendations.existingTags.length > 0 || tagRecommendations.newTags.length > 0)
    const hasFolders = folderRecommendations &&
      folderRecommendations.recommendedFolders.length > 0
    const hasDescription = descriptionGeneration &&
      descriptionGeneration.description

    return hasTags || hasFolders || hasDescription
  }

  // 当请求数据变化时自动获取推荐
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchRecommendations()
    }, 500) // 防抖处理

    return () => clearTimeout(timer)
  }, [request.title, request.content, request.description, request.url])

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 标题和操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Sparkles className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium text-foreground">AI智能推荐</span>
        </div>
        <div className="flex items-center space-x-2">
          {hasAcceptedAll ? (
            /* 已接受状态提示 */
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <Check className="w-4 h-4" />
                <span>已应用推荐</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setHasAcceptedAll(false)}
                disabled={disabled}
                className="h-8 text-xs text-muted-foreground hover:text-foreground"
                title="重新启用智能推荐"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                重新推荐
              </Button>
            </div>
          ) : (
            <>
              {/* 一键接受按钮 */}
              {hasRecommendations() && onAcceptAll && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleAcceptAll}
                  disabled={loading || disabled}
                  className="h-8 text-xs"
                >
                  <Check className="w-3 h-3 mr-1" />
                  一键接受
                </Button>
              )}
              {/* 刷新按钮 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={loading || disabled}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-primary" />
          <span className="ml-2 text-sm text-muted-foreground">AI正在分析内容...</span>
        </div>
      )}

      {/* 推荐内容区域 - 只在未接受状态下显示 */}
      {!hasAcceptedAll && (
        <>
          {/* 标签推荐 */}
          {showTagRecommendations && tagRecommendations && !loading && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-sm">
              <Tag className="w-4 h-4 mr-2" />
              推荐标签
              <Badge 
                variant="outline" 
                className={`ml-2 text-xs ${getConfidenceColor(tagRecommendations.confidence)}`}
              >
                置信度: {getConfidenceText(tagRecommendations.confidence)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* 现有标签推荐 */}
            {tagRecommendations.existingTags.length > 0 && (
              <div>
                <div className="text-xs text-muted-foreground mb-2">现有标签</div>
                <div className="flex flex-wrap gap-2">
                  {tagRecommendations.existingTags.map((tag, index) => {
                    const isSelected = selectedTags.includes(tag)
                    return (
                      <Badge
                        key={index}
                        variant={isSelected ? "default" : "secondary"}
                        className={`cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent'
                        }`}
                        onClick={() => handleTagClick(tag)}
                      >
                        {isSelected ? (
                          <Check className="w-3 h-3 mr-1" />
                        ) : (
                          <Plus className="w-3 h-3 mr-1" />
                        )}
                        {tag}
                      </Badge>
                    )
                  })}
                </div>
              </div>
            )}

            {/* 新标签建议 */}
            {tagRecommendations.newTags.length > 0 && (
              <div>
                {tagRecommendations.existingTags.length > 0 && <Separator />}
                <div className="text-xs text-muted-foreground mb-2">新标签建议</div>
                <div className="flex flex-wrap gap-2">
                  {tagRecommendations.newTags.map((tag, index) => {
                    const isSelected = selectedTags.includes(tag)
                    return (
                      <Badge
                        key={index}
                        variant={isSelected ? "default" : "outline"}
                        className={`cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent'
                        }`}
                        onClick={() => handleTagClick(tag)}
                      >
                        {isSelected ? (
                          <Check className="w-3 h-3 mr-1" />
                        ) : (
                          <Plus className="w-3 h-3 mr-1" />
                        )}
                        {tag}
                      </Badge>
                    )
                  })}
                </div>
              </div>
            )}

            {/* 推荐理由 */}
            {tagRecommendations.reasoning && (
              <div className="text-xs text-muted-foreground pt-2 border-t">
                {tagRecommendations.reasoning}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 文件夹推荐 */}
      {showFolderRecommendations && folderRecommendations && !loading && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-sm">
              <Folder className="w-4 h-4 mr-2" />
              推荐文件夹
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {folderRecommendations.recommendedFolders.map((folder, index) => {
              const isSelected = selectedFolder === folder.name
              return (
                <div
                  key={index}
                  className={`flex items-center justify-between p-2 rounded-md border cursor-pointer transition-colors ${
                    isSelected 
                      ? 'bg-primary/10 border-primary' 
                      : 'bg-background hover:bg-accent'
                  } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handleFolderClick(folder.name)}
                >
                  <div className="flex items-center space-x-2">
                    {isSelected ? (
                      <Check className="w-4 h-4 text-primary" />
                    ) : (
                      <Folder className="w-4 h-4 text-muted-foreground" />
                    )}
                    <span className="text-sm font-medium">{folder.name}</span>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getConfidenceColor(folder.confidence)}`}
                    >
                      {getConfidenceText(folder.confidence)}
                    </Badge>
                  </div>
                  {folder.reason && (
                    <span className="text-xs text-muted-foreground max-w-32 truncate">
                      {folder.reason}
                    </span>
                  )}
                </div>
              )
            })}

            {/* 推荐理由 */}
            {folderRecommendations.reasoning && (
              <div className="text-xs text-muted-foreground pt-2 border-t">
                {folderRecommendations.reasoning}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 描述生成 */}
      {showDescriptionGeneration && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center">
                <FileText className="w-4 h-4 mr-2" />
                智能描述生成
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={generateDescription}
                disabled={descriptionLoading || disabled || (!request.title && !request.content && !request.url)}
                className="h-7 text-xs"
              >
                {descriptionLoading ? (
                  <>
                    <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-3 h-3 mr-1" />
                    生成描述
                  </>
                )}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {descriptionGeneration ? (
              <div className="space-y-3">
                <div className="p-3 bg-muted/50 rounded-md">
                  <p className="text-sm text-foreground leading-relaxed">
                    {descriptionGeneration.description}
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Badge
                      variant="outline"
                      className={`${getConfidenceColor(descriptionGeneration.confidence)}`}
                    >
                      置信度: {getConfidenceText(descriptionGeneration.confidence)}
                    </Badge>
                    <span>字数: {descriptionGeneration.wordCount}</span>
                  </div>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleApplyDescription}
                    disabled={disabled}
                    className="h-7 text-xs"
                  >
                    <Check className="w-3 h-3 mr-1" />
                    应用描述
                  </Button>
                </div>
                {descriptionGeneration.reasoning && (
                  <div className="text-xs text-muted-foreground pt-2 border-t">
                    {descriptionGeneration.reasoning}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <FileText className="w-6 h-6 mx-auto mb-2 opacity-50" />
                <p className="text-xs">点击"生成描述"按钮，AI将为您生成合适的描述</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

          {/* 无推荐结果 */}
          {!loading && !error &&
           (!tagRecommendations || (tagRecommendations.existingTags.length === 0 && tagRecommendations.newTags.length === 0)) &&
           (!folderRecommendations || folderRecommendations.recommendedFolders.length === 0) &&
           !showDescriptionGeneration && (
            <div className="text-center py-8 text-muted-foreground">
              <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">请输入标题或内容以获取AI推荐</p>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default AIRecommendations